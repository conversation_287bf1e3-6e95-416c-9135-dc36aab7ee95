import React, { useMemo } from 'react';
import {
  createBrowserRouter,
  RouterProvider,
  Navigate,
  type RouteObject,
} from 'react-router-dom';
import { pageMap, type Permission } from './routeMap';
import { useAuthStore } from '@/store/authStore';
import Layout from '../components/Layout';
import Home from '../pages/Home';
import Login from '../pages/Login';
import NotFound from '../pages/NotFound';
// 路由守卫组件 用于保护需要认证的路由 如果用户未登录，会重定向到登录页面
import RequireAuth from '../components/RequireAuth';
// 认证重定向守卫组件 如果用户已登录，则重定向到指定页面
import RedirectIfAuthenticated from '../components/RedirectIfAuthenticated';
// 注册流程页面
import Register from '../pages/Register';
import EmailVerification from '../pages/Register/EmailVerification';
import AliasSetup from '../pages/Register/AliasSetup';
import PersonalInfo from '../pages/Register/PersonalInfo';
import PasswordSetup from '../pages/Register/PasswordSetup';
import { processPermissions } from '@/utils/permissionUtils';

/**
 * 根据权限动态生成路由
 * 扁平化路由：遍历权限树，为每个有URL的权限生成路由
 */
const buildRoutesFromPermissions = (
  permissions: Permission[]
): RouteObject[] => {
  const { permissionsUrls } = processPermissions(permissions);
  const routes: RouteObject[] = permissionsUrls.map(url => {
    if (pageMap[url]) {
      return {
        path: url,
        element: <RequireAuth>{React.createElement(pageMap[url])}</RequireAuth>,
      };
    }
  });

  return routes;
};

const AppRouter: React.FC = () => {
  const permissions = useAuthStore(state => state.permissions);
  const dynamicRoutes = buildRoutesFromPermissions(permissions);

  const router = useMemo(() => {
    // console.log('创建路由-----', dynamicRoutes, permissions);

    return createBrowserRouter([
      {
        path: '/login',
        element: (
          <RedirectIfAuthenticated>
            <Login />
          </RedirectIfAuthenticated>
        ),
      },
      // 注册流程路由
      {
        path: '/register',
        element: (
          <RedirectIfAuthenticated>
            <Register />
          </RedirectIfAuthenticated>
        ),
        children: [
          {
            path: 'email',
            element: <EmailVerification />,
          },
          {
            path: 'alias',
            element: <AliasSetup />,
          },
          {
            path: 'personal-info',
            element: <PersonalInfo />,
          },
          {
            path: 'password',
            element: <PasswordSetup />,
          },
        ],
      },
      // 主应用布局
      {
        path: '/',
        element: <Layout />,
        children: [
          {
            index: true,
            element: <Home />, // 首页所有人都能访问
          },
          // 动态生成的权限路由作为子路由
          ...dynamicRoutes,
        ],
      },
      // 通配符路由 - 404 页面
      {
        path: '*',
        // 在动态添加的路由页面丢失权限，会导致router重新渲染，路由会重定向到404页面。这里加了一个权限判断，如果没有权限跳转到login页面。
        element: (
          <RequireAuth checkPermission={false}>
            <NotFound />
          </RequireAuth>
        ),
      },
    ]);
  }, [permissions]);

  return <RouterProvider router={router} />;
};

export default AppRouter;
