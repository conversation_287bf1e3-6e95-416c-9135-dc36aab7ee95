import React from 'react';

interface FormButtonProps {
  text: string;
  onClick?: () => void;
  preventDefault?: boolean;
}

const FormButton: React.FC<FormButtonProps> = ({
  text,
  onClick,
  preventDefault = false,
}) => {
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (preventDefault) {
      e.preventDefault();
    }
    onClick?.();
  };

  return (
    <button
      onClick={handleClick}
      className="font-arial h-[63px] w-full cursor-pointer rounded-md border-none bg-[#ff5e13] text-[16px] text-black font-bold transition-colors hover:bg-[#e5541a]"
    >
      {text}
    </button>
  );
};

export default FormButton;
